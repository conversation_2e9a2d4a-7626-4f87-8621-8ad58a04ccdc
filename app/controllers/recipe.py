from tortoise.expressions import Q
from tortoise.transactions import atomic
from typing import List, Dict, Any

from app.core.crud import CRUDBase
from app.models.food_menu import Recipe, RecipeMeal, MealType
from app.schemas.recipe import RecipeCreate, RecipeUpdate, RecipeSearch
from app.controllers.utils import get_school_id
from app.core.ctx import CTX_USER_ID


class RecipeController(CRUDBase[Recipe, RecipeCreate, RecipeUpdate]):
    def __init__(self):
        super().__init__(model=Recipe)

    async def get_recipe_list(self, search: RecipeSearch):
        """获取食谱列表"""
        try:
            school_id = await get_school_id()
        except Exception:
            return {
                "total": 0,
                "data": []
            }

        q = Q(school_id=school_id)
        if search.recipe_date:
            q &= Q(recipe_date=search.recipe_date)

        # 分页查询
        total = await self.model.filter(q).count()
        recipes = await self.model.filter(q).prefetch_related('meals').limit(search.size).offset((search.current - 1) * search.size).order_by('-recipe_date')

        result = []
        for recipe in recipes:
            recipe_dict = await recipe.to_dict()
            
            # 获取餐次信息
            meals = []
            for meal in recipe.meals:
                meal_dict = await meal.to_dict()
                # 添加餐次类型名称
                meal_type_name = self._get_meal_type_name(meal.meal_type)
                meal_dict['meal_type_name'] = meal_type_name
                meals.append(meal_dict)
            
            recipe_dict['meals'] = meals
            result.append(recipe_dict)

        return {
            "total": total,
            "data": result
        }

    async def get_recipe_detail(self, recipe_id: int):
        """获取食谱详情"""
        try:
            school_id = await get_school_id()
        except Exception:
            raise ValueError("无法获取学校信息")

        recipe = await self.model.filter(id=recipe_id, school_id=school_id).prefetch_related('meals').first()
        if not recipe:
            raise ValueError("食谱不存在或不属于当前学校")

        recipe_dict = await recipe.to_dict()
        
        # 获取餐次信息
        meals = []
        for meal in recipe.meals:
            meal_dict = await meal.to_dict()
            meal_type_name = self._get_meal_type_name(meal.meal_type)
            meal_dict['meal_type_name'] = meal_type_name
            meals.append(meal_dict)
        
        recipe_dict['meals'] = meals
        return recipe_dict

    @atomic()
    async def create_recipe(self, obj_in: RecipeCreate):
        """创建食谱"""
        school_id = await get_school_id()

        # 检查是否已存在相同日期的食谱
        existing_recipe = await self.model.filter(recipe_date=obj_in.recipe_date, school_id=school_id).first()
        if existing_recipe:
            raise ValueError(f"日期 {obj_in.recipe_date} 的食谱已存在")

        # 创建食谱主记录
        recipe = Recipe(
            recipe_date=obj_in.recipe_date,
            recipe_name=obj_in.recipe_name,
            school_id=school_id,
            is_active=obj_in.is_active
        )
        await recipe.save()

        # 创建餐次记录
        for meal_data in obj_in.meals:
            # 检查餐次类型是否重复
            existing_meal = await RecipeMeal.filter(recipe=recipe, meal_type=meal_data.meal_type).first()
            if existing_meal:
                raise ValueError(f"餐次类型 {self._get_meal_type_name(meal_data.meal_type)} 已存在")

            meal = RecipeMeal(
                recipe=recipe,
                meal_type=meal_data.meal_type,
                dish_list=meal_data.dish_list,
                is_active=meal_data.is_active
            )
            await meal.save()

        return recipe

    @atomic()
    async def update_recipe(self, recipe_id: int, obj_in: RecipeUpdate):
        """更新食谱"""
        school_id = await get_school_id()

        # 获取食谱
        recipe = await self.model.filter(id=recipe_id, school_id=school_id).first()
        if not recipe:
            raise ValueError("食谱不存在或不属于当前学校")

        # 更新食谱基本信息
        if obj_in.recipe_date is not None:
            # 检查新日期是否与其他食谱冲突
            existing_recipe = await self.model.filter(
                recipe_date=obj_in.recipe_date, 
                school_id=school_id
            ).exclude(id=recipe_id).first()
            if existing_recipe:
                raise ValueError(f"日期 {obj_in.recipe_date} 的食谱已存在")
            recipe.recipe_date = obj_in.recipe_date

        if obj_in.recipe_name is not None:
            recipe.recipe_name = obj_in.recipe_name
        if obj_in.is_active is not None:
            recipe.is_active = obj_in.is_active

        await recipe.save()

        # 更新餐次信息
        if obj_in.meals is not None:
            # 删除现有餐次
            await RecipeMeal.filter(recipe=recipe).delete()
            
            # 创建新的餐次
            for meal_data in obj_in.meals:
                meal = RecipeMeal(
                    recipe=recipe,
                    meal_type=meal_data.meal_type,
                    dish_list=meal_data.dish_list,
                    is_active=meal_data.is_active
                )
                await meal.save()

        return recipe

    @atomic()
    async def delete_recipe(self, recipe_id: int):
        """删除食谱"""
        school_id = await get_school_id()

        recipe = await self.model.filter(id=recipe_id, school_id=school_id).first()
        if not recipe:
            raise ValueError("食谱不存在或不属于当前学校")

        # 删除关联的餐次
        await RecipeMeal.filter(recipe=recipe).delete()
        
        # 删除食谱
        await recipe.delete()

    def _get_meal_type_name(self, meal_type: int) -> str:
        """获取餐次类型名称"""
        meal_type_map = {
            MealType.BREAKFAST: "早餐",
            MealType.MORNING_SNACK: "上午加餐",
            MealType.LUNCH: "午餐",
            MealType.AFTERNOON_SNACK: "下午加餐",
            MealType.DINNER: "晚餐"
        }
        return meal_type_map.get(meal_type, f"未知餐次({meal_type})")

    async def get_recipe_by_date_for_order(self, recipe_date):
        """根据日期获取食谱信息用于下单"""
        try:
            school_id = await get_school_id()
        except Exception:
            raise ValueError("无法获取学校信息")

        recipe = await self.model.filter(
            recipe_date=recipe_date, 
            school_id=school_id
        ).prefetch_related('meals').first()
        
        if not recipe:
            raise ValueError(f"日期 {recipe_date} 的食谱不存在")

        # 合并所有餐次的菜品信息用于下单
        all_dishes = []
        for meal in recipe.meals:
            if meal.dish_list:
                all_dishes.extend(meal.dish_list)

        return {
            "recipe_date": recipe.recipe_date,
            "dish_list": all_dishes,
            "meals": [await meal.to_dict() for meal in recipe.meals]
        }


recipe_controller = RecipeController()
