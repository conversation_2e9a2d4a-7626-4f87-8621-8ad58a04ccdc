import { request } from '@/utils'

export default {
  login: (data) => request.post('/base/access_token', data, { noNeedToken: true }),
  getUserInfo: () => request.get('/base/userinfo'),
  getUserMenu: () => request.get('/base/usermenu'),
  getUserApi: () => request.get('/base/userapi'),
  // profile
  updatePassword: (data = {}) => request.post('/base/update_password', data),
  // users
  getUserList: (params = {}) => request.get('/user/list', { params }),
  getUserById: (params = {}) => request.get('/user/get', { params }),
  createUser: (data = {}) => request.post('/user/create', data),
  updateUser: (data = {}) => request.post('/user/update', data),
  deleteUser: (params = {}) => request.delete(`/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/user/reset_password`, data),
  // role
  getRoleList: (params = {}) => request.get('/role/list', { params }),
  createRole: (data = {}) => request.post('/role/create', data),
  updateRole: (data = {}) => request.post('/role/update', data),
  deleteRole: (params = {}) => request.delete('/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/role/authorized', { params }),
  // menus
  getMenus: (params = {}) => request.get('/menu/list', { params }),
  createMenu: (data = {}) => request.post('/menu/create', data),
  updateMenu: (data = {}) => request.post('/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/menu/delete', { params }),
  // apis
  getApis: (params = {}) => request.get('/api/list', { params }),
  createApi: (data = {}) => request.post('/api/create', data),
  updateApi: (data = {}) => request.post('/api/update', data),
  deleteApi: (params = {}) => request.delete('/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/api/refresh', data),
  // depts
  getDepts: (params = {}) => request.get('/dept/list', { params }),
  createDept: (data = {}) => request.post('/dept/create', data),
  updateDept: (data = {}) => request.post('/dept/update', data),
  deleteDept: (params = {}) => request.delete('/dept/delete', { params }),
  getSuppliers: (params = {}) => request.get('/dept/suppliers', { params }),
  generateSchoolToken: (data = {}) => request.post('/dept/school/generate-token', data),
  // 供应商操作
  addSupplier: (params = {}) => request.post('/dept/supplier/add', params),
  removeSupplier: (params = {}) => request.post('/dept/supplier/remove', params),
  filterSuppliers: (params = {}) => request.get('/dept/supplier/filter', { params }),
  // 班级和学生数量
  getClassList: (params = {}) => request.get('/dept/class/list', { params }),
  updateStudentCount: (data = {}) => request.post('/dept/student-count/update', data),
  getTotalStudentCount: () => request.get('/dept/total-student-count'),
  // auditlog
  getAuditLogList: (params = {}) => request.get('/auditlog/list', { params }),
  // 单位管理
  getUnitList: (params) => request.get('/unit/list', { params }),
  createUnit: (data) => request.post('/unit/create', data),
  updateUnit: (data) => request.post('/unit/update', data),
  deleteUnit: (data) => request.delete('/unit/delete', { params: data }),
  // 营养素管理
  getNutrientList: (params) => request.get('/nutrient/list', { params }),
  createNutrient: (data) => request.post('/nutrient/create', data),
  updateNutrient: (data) => request.post('/nutrient/update', data),
  deleteNutrient: (data) => request.delete('/nutrient/delete', { params: data }),
  // 食材管理
  getFoodStuffList: (params) => request.get('/food-stuff/list', { params }),
  createFoodStuff: (data) => request.post('/food-stuff/create', data),
  updateFoodStuff: (data) => request.post('/food-stuff/update', data),
  deleteFoodStuff: (data) => request.delete('/food-stuff/delete', { params: data }),
  // 食材库存管理
  getFoodStuffStoreList: (params) => request.get('/food-stuff-store/list', { params }),
  updateFoodStuffStore: (data) => request.post('/food-stuff-store/update', data),
  // 供应商接口
  getSupplierList: (params) => request.get('/dept/suppliers', { params }),
  // 订单接口
  createOrder: (data) => request.post('/orders/create', data),
  getOrderList: (params) => request.get('/orders/list', { params }),
  shipment: (data) => request.post('/orders/shipment', data),
  getOrderDetail: (data) => request.post('/order/detail', { order_id: data.orderId || data.order_id, token: data.token }),
  confirmReceive: (data) => {
    const formData = new FormData();
    formData.append('order_id', data.order_id);
    formData.append('token', data.token);
    return request.post('/base/inbound', formData);
  },
  // 食谱接口（新版）
  getRecipeList: (params) => request.get('/food-menu/list', { params }),
  getRecipeById: (params) => request.get('/food-menu/get', { params }),
  createRecipe: (data) => request.post('/food-menu/create', data),
  updateRecipe: (data) => request.post('/food-menu/update', { params: { id: data.id }, data }),
  deleteRecipe: (params) => request.delete('/food-menu/delete', { params }),
  getRecipeByDate: (params) => request.get('/food-menu/by-date', { params }),
  // 菜品管理接口
  getDishList: (params) => request.get('/dish/list', { params }),
  getDishById: (params) => request.get('/dish/get', { params }),
  createDish: (data) => request.post('/dish/create', data),
  updateDish: (data) => request.post('/dish/update', data),
  deleteDish: (params) => request.delete('/dish/delete', { params }),
}
